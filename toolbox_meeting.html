<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toolbox Meeting</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            padding: 10px;
            font-size: 14px;
            line-height: 1.4;
        }

        .container {
            max-width: 100%;
            background-color: white;
            border: 3px solid #FFD700;
            border-radius: 15px;
            padding: 20px;
            margin: 0 auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .section {
            margin-bottom: 20px;
        }

        .section-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 15px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            background-color: white;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }

        td {
            font-size: 14px;
            min-height: 35px;
        }

        .indisipliner-table td:nth-child(2),
        .indisipliner-table td:nth-child(4) {
            background-color: #fafafa;
            border: 1px solid #ccc;
        }

        .pic-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            margin-bottom: 15px;
            background-color: #fafafa;
        }

        .pic-input:focus {
            outline: none;
            border-color: #FFD700;
            background-color: white;
        }

        .scrollable-table {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .scrollable-table table {
            margin-bottom: 0;
        }

        .scrollable-table::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-table::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .scrollable-table::-webkit-scrollbar-thumb {
            background: #FFD700;
            border-radius: 3px;
        }

        .scrollable-table::-webkit-scrollbar-thumb:hover {
            background: #e6c200;
        }

        .data-row td {
            height: 40px;
            background-color: #fafafa;
        }

        .keterangan-col {
            width: 40%;
            text-align: left;
            padding-left: 10px;
        }

        .no-col {
            width: 10%;
        }

        .spk-col, .pic-col {
            width: 25%;
        }

        .editable-cell {
            background-color: white !important;
            cursor: text;
            position: relative;
        }

        .editable-cell:hover {
            background-color: #f0f8ff !important;
        }

        .cell-input {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            padding: 4px;
            font-size: 14px;
            text-align: center;
            outline: none;
        }

        .cell-input:focus {
            background-color: #fff;
            border: 2px solid #FFD700;
        }

        .keterangan-input {
            text-align: left !important;
            padding-left: 8px !important;
        }

        .add-row-btn {
            background-color: #FFD700;
            color: #333;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 10px;
            font-weight: bold;
        }

        .add-row-btn:hover {
            background-color: #e6c200;
        }

        .save-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
            width: 100%;
            font-weight: bold;
        }

        .save-btn:hover {
            background-color: #218838;
        }

        @media (max-width: 480px) {
            .container {
                padding: 15px;
                border-width: 2px;
            }
            
            .title {
                font-size: 16px;
            }
            
            th, td {
                padding: 6px 4px;
                font-size: 13px;
            }
            
            .pic-input {
                padding: 8px;
                font-size: 13px;
            }
            
            .section-title {
                font-size: 14px;
            }
        }

        @media (max-width: 360px) {
            body {
                padding: 5px;
            }
            
            .container {
                padding: 10px;
            }
            
            th, td {
                padding: 4px 2px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">TOOLBOX MEETING</div>
        
        <!-- Tabel Indisipliner -->
        <div class="section">
            <table class="indisipliner-table">
                <tr>
                    <th class="no-col">No</th>
                    <th>Indisipliner</th>
                    <th class="no-col">No</th>
                    <th>Cost</th>
                </tr>
                <tr>
                    <td>I</td>
                    <td class="editable-cell"><input type="text" class="cell-input" placeholder="Isi indisipliner..."></td>
                    <td>1</td>
                    <td class="editable-cell"><input type="number" class="cell-input" placeholder="0"></td>
                </tr>
                <tr>
                    <td>S</td>
                    <td class="editable-cell"><input type="text" class="cell-input" placeholder="Isi indisipliner..."></td>
                    <td>2</td>
                    <td class="editable-cell"><input type="number" class="cell-input" placeholder="0"></td>
                </tr>
                <tr>
                    <td>A</td>
                    <td class="editable-cell"><input type="text" class="cell-input" placeholder="Isi indisipliner..."></td>
                    <td>3</td>
                    <td class="editable-cell"><input type="number" class="cell-input" placeholder="0"></td>
                </tr>
            </table>
        </div>

        <!-- Input PIC Alfa -->
        <div class="section">
            <input type="text" class="pic-input" placeholder="PIC Alfa" />
        </div>

        <!-- Target Hari Ini -->
        <div class="section">
            <div class="section-title">Target Hari Ini Tgl : <input type="date" style="margin-left: 10px; padding: 5px; border: 1px solid #ddd; border-radius: 3px;"></div>
            <div class="scrollable-table">
                <table id="targetTable">
                    <thead>
                        <tr>
                            <th class="no-col">No</th>
                            <th class="spk-col">SPK</th>
                            <th class="pic-col">PIC</th>
                            <th class="keterangan-col">Keterangan</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="data-row">
                            <td class="editable-cell"><input type="number" class="cell-input" placeholder="1"></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="SPK..."></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="PIC..."></td>
                            <td class="editable-cell keterangan-col"><input type="text" class="cell-input keterangan-input" placeholder="Keterangan..."></td>
                        </tr>
                        <tr class="data-row">
                            <td class="editable-cell"><input type="number" class="cell-input" placeholder="2"></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="SPK..."></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="PIC..."></td>
                            <td class="editable-cell keterangan-col"><input type="text" class="cell-input keterangan-input" placeholder="Keterangan..."></td>
                        </tr>
                        <tr class="data-row">
                            <td class="editable-cell"><input type="number" class="cell-input" placeholder="3"></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="SPK..."></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="PIC..."></td>
                            <td class="editable-cell keterangan-col"><input type="text" class="cell-input keterangan-input" placeholder="Keterangan..."></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <button class="add-row-btn" onclick="addRow('targetTable')">+ Tambah Baris</button>
        </div>

        <!-- Outstanding Hari Kemarin -->
        <div class="section">
            <div class="section-title">Outstanding Hari Kemarin Tgl : <input type="date" style="margin-left: 10px; padding: 5px; border: 1px solid #ddd; border-radius: 3px;"></div>
            <div class="scrollable-table">
                <table id="outstandingTable">
                    <thead>
                        <tr>
                            <th class="no-col">No</th>
                            <th class="spk-col">SPK</th>
                            <th class="pic-col">PIC</th>
                            <th class="keterangan-col">Keterangan</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="data-row">
                            <td class="editable-cell"><input type="number" class="cell-input" placeholder="1"></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="SPK..."></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="PIC..."></td>
                            <td class="editable-cell keterangan-col"><input type="text" class="cell-input keterangan-input" placeholder="Keterangan..."></td>
                        </tr>
                        <tr class="data-row">
                            <td class="editable-cell"><input type="number" class="cell-input" placeholder="2"></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="SPK..."></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="PIC..."></td>
                            <td class="editable-cell keterangan-col"><input type="text" class="cell-input keterangan-input" placeholder="Keterangan..."></td>
                        </tr>
                        <tr class="data-row">
                            <td class="editable-cell"><input type="number" class="cell-input" placeholder="3"></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="SPK..."></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="PIC..."></td>
                            <td class="editable-cell keterangan-col"><input type="text" class="cell-input keterangan-input" placeholder="Keterangan..."></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <button class="add-row-btn" onclick="addRow('outstandingTable')">+ Tambah Baris</button>
        </div>

        <!-- Tombol Simpan -->
        <button class="save-btn" onclick="saveData()">💾 SIMPAN DATA</button>
    </div>

    <script>
        let rowCounter = { targetTable: 4, outstandingTable: 4 };

        function addRow(tableId) {
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const newRow = document.createElement('tr');
            newRow.className = 'data-row';

            const rowNum = rowCounter[tableId];
            newRow.innerHTML = `
                <td class="editable-cell"><input type="number" class="cell-input" placeholder="${rowNum}"></td>
                <td class="editable-cell"><input type="text" class="cell-input" placeholder="SPK..."></td>
                <td class="editable-cell"><input type="text" class="cell-input" placeholder="PIC..."></td>
                <td class="editable-cell keterangan-col"><input type="text" class="cell-input keterangan-input" placeholder="Keterangan..."></td>
            `;

            tbody.appendChild(newRow);
            rowCounter[tableId]++;
        }

        function saveData() {
            const data = {
                timestamp: new Date().toISOString(),
                indisipliner: [],
                picAlfa: document.querySelector('.pic-input').value,
                targetHariIni: {
                    tanggal: document.querySelector('input[type="date"]').value,
                    data: []
                },
                outstandingKemarin: {
                    tanggal: document.querySelectorAll('input[type="date"]')[1].value,
                    data: []
                }
            };

            // Ambil data indisipliner
            const indisiplinerRows = document.querySelectorAll('.indisipliner-table tr:not(:first-child)');
            indisiplinerRows.forEach((row, index) => {
                const inputs = row.querySelectorAll('input');
                data.indisipliner.push({
                    type: ['I', 'S', 'A'][index],
                    description: inputs[0].value,
                    cost: inputs[1].value
                });
            });

            // Ambil data target hari ini
            const targetRows = document.querySelectorAll('#targetTable tbody tr');
            targetRows.forEach(row => {
                const inputs = row.querySelectorAll('input');
                if (inputs[1].value || inputs[2].value || inputs[3].value) {
                    data.targetHariIni.data.push({
                        no: inputs[0].value,
                        spk: inputs[1].value,
                        pic: inputs[2].value,
                        keterangan: inputs[3].value
                    });
                }
            });

            // Ambil data outstanding kemarin
            const outstandingRows = document.querySelectorAll('#outstandingTable tbody tr');
            outstandingRows.forEach(row => {
                const inputs = row.querySelectorAll('input');
                if (inputs[1].value || inputs[2].value || inputs[3].value) {
                    data.outstandingKemarin.data.push({
                        no: inputs[0].value,
                        spk: inputs[1].value,
                        pic: inputs[2].value,
                        keterangan: inputs[3].value
                    });
                }
            });

            // Simpan ke localStorage
            localStorage.setItem('toolboxMeetingData', JSON.stringify(data));

            // Tampilkan konfirmasi
            alert('Data berhasil disimpan!\n\nData tersimpan di browser dan dapat diakses kembali.');

            // Optional: Download sebagai JSON
            downloadData(data);
        }

        function downloadData(data) {
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `toolbox_meeting_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        // Load data saat halaman dimuat
        window.onload = function() {
            const savedData = localStorage.getItem('toolboxMeetingData');
            if (savedData) {
                const data = JSON.parse(savedData);

                // Load PIC Alfa
                if (data.picAlfa) {
                    document.querySelector('.pic-input').value = data.picAlfa;
                }

                // Load tanggal
                const dateInputs = document.querySelectorAll('input[type="date"]');
                if (data.targetHariIni.tanggal) {
                    dateInputs[0].value = data.targetHariIni.tanggal;
                }
                if (data.outstandingKemarin.tanggal) {
                    dateInputs[1].value = data.outstandingKemarin.tanggal;
                }

                console.log('Data berhasil dimuat dari penyimpanan lokal');
            }
        };
    </script>
</body>
</html>
