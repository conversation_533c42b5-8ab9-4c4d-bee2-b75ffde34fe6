/**
 * 🌥️ Cloud Storage Integration for Toolbox Meeting System
 * Supports: Google Drive, Dropbox, Firebase
 * Features: Auto-backup, Real-time sync, Team collaboration
 */

class CloudStorageManager {
    constructor() {
        this.providers = {
            googleDrive: new GoogleDriveProvider(),
            dropbox: new DropboxProvider(),
            firebase: new FirebaseProvider()
        };
        this.currentProvider = null;
        this.autoBackupInterval = null;
    }

    // Initialize cloud storage
    async init(provider = 'googleDrive') {
        try {
            this.currentProvider = this.providers[provider];
            await this.currentProvider.init();
            this.startAutoBackup();
            console.log(`✅ ${provider} initialized successfully`);
            return true;
        } catch (error) {
            console.error(`❌ Failed to initialize ${provider}:`, error);
            return false;
        }
    }

    // Save data to cloud
    async saveToCloud(data) {
        if (!this.currentProvider) {
            throw new Error('Cloud provider not initialized');
        }

        const filename = `toolbox_meeting_${new Date().toISOString().split('T')[0]}.json`;
        return await this.currentProvider.saveFile(filename, JSON.stringify(data, null, 2));
    }

    // Load data from cloud
    async loadFromCloud(filename) {
        if (!this.currentProvider) {
            throw new Error('Cloud provider not initialized');
        }

        return await this.currentProvider.loadFile(filename);
    }

    // List all backup files
    async listBackups() {
        if (!this.currentProvider) {
            throw new Error('Cloud provider not initialized');
        }

        return await this.currentProvider.listFiles('toolbox_meeting_');
    }

    // Auto backup every 30 minutes
    startAutoBackup() {
        this.autoBackupInterval = setInterval(async () => {
            try {
                const data = localStorage.getItem('allToolboxData');
                if (data) {
                    await this.saveToCloud(JSON.parse(data));
                    console.log('🔄 Auto backup completed');
                }
            } catch (error) {
                console.error('❌ Auto backup failed:', error);
            }
        }, 30 * 60 * 1000); // 30 minutes
    }

    // Stop auto backup
    stopAutoBackup() {
        if (this.autoBackupInterval) {
            clearInterval(this.autoBackupInterval);
            this.autoBackupInterval = null;
        }
    }
}

// Google Drive Provider
class GoogleDriveProvider {
    constructor() {
        this.apiKey = 'YOUR_GOOGLE_DRIVE_API_KEY';
        this.clientId = 'YOUR_GOOGLE_CLIENT_ID';
        this.folderId = null;
    }

    async init() {
        // Load Google Drive API
        await this.loadGoogleAPI();
        await this.authenticate();
        await this.createAppFolder();
    }

    async loadGoogleAPI() {
        return new Promise((resolve, reject) => {
            if (window.gapi) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://apis.google.com/js/api.js';
            script.onload = () => {
                gapi.load('auth2:client', resolve);
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    async authenticate() {
        await gapi.client.init({
            apiKey: this.apiKey,
            clientId: this.clientId,
            discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest'],
            scope: 'https://www.googleapis.com/auth/drive.file'
        });

        const authInstance = gapi.auth2.getAuthInstance();
        if (!authInstance.isSignedIn.get()) {
            await authInstance.signIn();
        }
    }

    async createAppFolder() {
        const response = await gapi.client.drive.files.list({
            q: "name='Toolbox Meeting Backups' and mimeType='application/vnd.google-apps.folder'",
            spaces: 'drive'
        });

        if (response.result.files.length === 0) {
            const folderResponse = await gapi.client.drive.files.create({
                resource: {
                    name: 'Toolbox Meeting Backups',
                    mimeType: 'application/vnd.google-apps.folder'
                }
            });
            this.folderId = folderResponse.result.id;
        } else {
            this.folderId = response.result.files[0].id;
        }
    }

    async saveFile(filename, content) {
        const metadata = {
            name: filename,
            parents: [this.folderId]
        };

        const form = new FormData();
        form.append('metadata', new Blob([JSON.stringify(metadata)], {type: 'application/json'}));
        form.append('file', new Blob([content], {type: 'application/json'}));

        const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
            method: 'POST',
            headers: new Headers({
                'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`
            }),
            body: form
        });

        return response.json();
    }

    async loadFile(filename) {
        const response = await gapi.client.drive.files.list({
            q: `name='${filename}' and parents in '${this.folderId}'`,
            spaces: 'drive'
        });

        if (response.result.files.length > 0) {
            const fileId = response.result.files[0].id;
            const fileResponse = await gapi.client.drive.files.get({
                fileId: fileId,
                alt: 'media'
            });
            return JSON.parse(fileResponse.body);
        }
        return null;
    }

    async listFiles(prefix = '') {
        const response = await gapi.client.drive.files.list({
            q: `name contains '${prefix}' and parents in '${this.folderId}'`,
            spaces: 'drive',
            orderBy: 'modifiedTime desc'
        });

        return response.result.files.map(file => ({
            name: file.name,
            id: file.id,
            modifiedTime: file.modifiedTime,
            size: file.size
        }));
    }
}

// Dropbox Provider
class DropboxProvider {
    constructor() {
        this.accessToken = 'YOUR_DROPBOX_ACCESS_TOKEN';
        this.apiUrl = 'https://api.dropboxapi.com/2';
    }

    async init() {
        // Verify token
        const response = await fetch(`${this.apiUrl}/users/get_current_account`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('Invalid Dropbox access token');
        }
    }

    async saveFile(filename, content) {
        const response = await fetch('https://content.dropboxapi.com/2/files/upload', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.accessToken}`,
                'Content-Type': 'application/octet-stream',
                'Dropbox-API-Arg': JSON.stringify({
                    path: `/Toolbox Meeting Backups/${filename}`,
                    mode: 'overwrite'
                })
            },
            body: content
        });

        return response.json();
    }

    async loadFile(filename) {
        const response = await fetch('https://content.dropboxapi.com/2/files/download', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.accessToken}`,
                'Dropbox-API-Arg': JSON.stringify({
                    path: `/Toolbox Meeting Backups/${filename}`
                })
            }
        });

        if (response.ok) {
            const content = await response.text();
            return JSON.parse(content);
        }
        return null;
    }

    async listFiles(prefix = '') {
        const response = await fetch(`${this.apiUrl}/files/list_folder`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                path: '/Toolbox Meeting Backups'
            })
        });

        const data = await response.json();
        return data.entries
            .filter(file => file.name.includes(prefix))
            .map(file => ({
                name: file.name,
                id: file.id,
                modifiedTime: file.client_modified,
                size: file.size
            }));
    }
}

// Firebase Provider
class FirebaseProvider {
    constructor() {
        this.config = {
            apiKey: "YOUR_FIREBASE_API_KEY",
            authDomain: "your-project.firebaseapp.com",
            databaseURL: "https://your-project.firebaseio.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id"
        };
    }

    async init() {
        // Load Firebase SDK
        await this.loadFirebaseSDK();
        
        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(this.config);
        }

        // Initialize services
        this.db = firebase.firestore();
        this.storage = firebase.storage();
        
        // Anonymous authentication
        await firebase.auth().signInAnonymously();
    }

    async loadFirebaseSDK() {
        return new Promise((resolve, reject) => {
            if (window.firebase) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js';
            script.onload = () => {
                const firestoreScript = document.createElement('script');
                firestoreScript.src = 'https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore.js';
                firestoreScript.onload = resolve;
                firestoreScript.onerror = reject;
                document.head.appendChild(firestoreScript);
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    async saveFile(filename, content) {
        const docRef = this.db.collection('toolbox_backups').doc(filename);
        await docRef.set({
            content: content,
            timestamp: firebase.firestore.FieldValue.serverTimestamp(),
            filename: filename
        });
        return { id: docRef.id };
    }

    async loadFile(filename) {
        const docRef = this.db.collection('toolbox_backups').doc(filename);
        const doc = await docRef.get();
        
        if (doc.exists) {
            return JSON.parse(doc.data().content);
        }
        return null;
    }

    async listFiles(prefix = '') {
        const snapshot = await this.db.collection('toolbox_backups')
            .where('filename', '>=', prefix)
            .where('filename', '<', prefix + '\uf8ff')
            .orderBy('timestamp', 'desc')
            .get();

        return snapshot.docs.map(doc => {
            const data = doc.data();
            return {
                name: data.filename,
                id: doc.id,
                modifiedTime: data.timestamp?.toDate()?.toISOString(),
                size: data.content?.length || 0
            };
        });
    }
}

// Real-time sync functionality
class RealTimeSync {
    constructor(cloudManager) {
        this.cloudManager = cloudManager;
        this.syncInterval = null;
        this.lastSyncTime = null;
    }

    startSync(intervalMinutes = 5) {
        this.syncInterval = setInterval(async () => {
            await this.syncData();
        }, intervalMinutes * 60 * 1000);
    }

    stopSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
    }

    async syncData() {
        try {
            // Get latest backup from cloud
            const backups = await this.cloudManager.listBackups();
            if (backups.length > 0) {
                const latestBackup = backups[0];
                const cloudData = await this.cloudManager.loadFromCloud(latestBackup.name);
                
                // Compare with local data
                const localData = JSON.parse(localStorage.getItem('allToolboxData') || '[]');
                
                if (this.shouldUpdateLocal(cloudData, localData)) {
                    localStorage.setItem('allToolboxData', JSON.stringify(cloudData));
                    this.notifyDataUpdate();
                }
            }
        } catch (error) {
            console.error('Sync failed:', error);
        }
    }

    shouldUpdateLocal(cloudData, localData) {
        if (!cloudData || !Array.isArray(cloudData)) return false;
        if (cloudData.length > localData.length) return true;
        
        // Check timestamps
        const latestCloudTime = Math.max(...cloudData.map(item => new Date(item.timestamp).getTime()));
        const latestLocalTime = Math.max(...localData.map(item => new Date(item.timestamp).getTime()));
        
        return latestCloudTime > latestLocalTime;
    }

    notifyDataUpdate() {
        // Show notification to user
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('Toolbox Meeting', {
                body: 'Data telah diperbarui dari cloud backup',
                icon: '/favicon.ico'
            });
        }
        
        // Trigger UI update
        window.dispatchEvent(new CustomEvent('dataUpdated'));
    }
}

// Usage example and integration
window.ToolboxCloudManager = {
    cloudManager: new CloudStorageManager(),
    realTimeSync: null,

    async initialize(provider = 'googleDrive') {
        const success = await this.cloudManager.init(provider);
        if (success) {
            this.realTimeSync = new RealTimeSync(this.cloudManager);
            this.realTimeSync.startSync(5); // Sync every 5 minutes
            
            // Request notification permission
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission();
            }
        }
        return success;
    },

    async backup() {
        const data = JSON.parse(localStorage.getItem('allToolboxData') || '[]');
        return await this.cloudManager.saveToCloud(data);
    },

    async restore(filename) {
        const data = await this.cloudManager.loadFromCloud(filename);
        if (data) {
            localStorage.setItem('allToolboxData', JSON.stringify(data));
            window.location.reload();
        }
        return data;
    },

    async getBackupList() {
        return await this.cloudManager.listBackups();
    }
};

// Auto-initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if user is admin
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
    if (currentUser.role === 'admin') {
        // Uncomment the provider you want to use
        // window.ToolboxCloudManager.initialize('googleDrive');
        // window.ToolboxCloudManager.initialize('dropbox');
        // window.ToolboxCloudManager.initialize('firebase');
    }
});
