<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#f5f5f5"
    tools:context=".MainActivity">

    <!-- Header dengan logo/title -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFD700"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="TOOLBOX MEETING"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center" />

    </LinearLayout>

    <!-- WebView untuk menampilkan HTML -->
    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="4dp"
        android:background="@android:color/white" />

    <!-- Footer dengan info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#333333"
        android:orientation="horizontal"
        android:padding="8dp"
        android:gravity="center">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="© 2024 Toolbox Meeting App"
            android:textColor="#ffffff"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>
