<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toolbox Meeting - Team Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            padding: 10px;
            font-size: 14px;
            line-height: 1.4;
        }

        .container {
            max-width: 100%;
            background-color: white;
            border: 3px solid #FFD700;
            border-radius: 15px;
            padding: 20px;
            margin: 0 auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .auth-container {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            border: 3px solid #FFD700;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .header-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #FFD700;
            margin: -20px -20px 20px -20px;
            padding: 15px 20px;
            border-radius: 12px 12px 0 0;
        }

        .user-info {
            font-weight: bold;
            color: #333;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .auth-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input, .form-group select {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #FFD700;
        }

        .auth-btn {
            background: #FFD700;
            color: #333;
            border: none;
            padding: 12px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
        }

        .auth-btn:hover {
            background: #e6c200;
        }

        .error-msg {
            color: #dc3545;
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
        }

        .success-msg {
            color: #28a745;
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
        }

        .section {
            margin-bottom: 20px;
        }

        .section-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 15px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            background-color: white;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }

        td {
            font-size: 14px;
            min-height: 35px;
        }

        .editable-cell {
            background-color: white !important;
            cursor: text;
            position: relative;
        }

        .editable-cell:hover {
            background-color: #f0f8ff !important;
        }

        .cell-input {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            padding: 4px;
            font-size: 14px;
            text-align: center;
            outline: none;
        }

        .cell-input:focus {
            background-color: #fff;
            border: 2px solid #FFD700;
        }

        .readonly-cell {
            background-color: #f8f9fa !important;
            color: #666;
        }

        .readonly-input {
            background-color: #f8f9fa !important;
            color: #666;
            cursor: not-allowed;
        }

        .pic-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            margin-bottom: 15px;
            background-color: #fafafa;
        }

        .pic-input:focus {
            outline: none;
            border-color: #FFD700;
            background-color: white;
        }

        .scrollable-table {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .scrollable-table table {
            margin-bottom: 0;
        }

        .add-row-btn, .save-btn, .export-btn {
            background-color: #FFD700;
            color: #333;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }

        .save-btn {
            background-color: #28a745;
            color: white;
            padding: 12px 30px;
            font-size: 16px;
            width: calc(100% - 10px);
        }

        .export-btn {
            background-color: #17a2b8;
            color: white;
        }

        .add-row-btn:hover {
            background-color: #e6c200;
        }

        .save-btn:hover {
            background-color: #218838;
        }

        .export-btn:hover {
            background-color: #138496;
        }

        .admin-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 2px solid #FFD700;
        }

        .admin-title {
            color: #333;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .hidden {
            display: none !important;
        }

        .readonly-mode {
            pointer-events: none;
            opacity: 0.7;
        }

        @media (max-width: 480px) {
            .container, .auth-container {
                padding: 15px;
                border-width: 2px;
            }
            
            .title {
                font-size: 16px;
            }
            
            th, td {
                padding: 6px 4px;
                font-size: 13px;
            }
            
            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Login Form -->
    <div id="authContainer" class="auth-container">
        <div class="title">TOOLBOX MEETING</div>
        <div class="title" style="font-size: 14px; margin-bottom: 30px;">Team Access Portal</div>
        
        <form class="auth-form" onsubmit="handleLogin(event)">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" required>
            </div>
            
            <div class="form-group">
                <label for="role">Role:</label>
                <select id="role" required>
                    <option value="">Pilih Role</option>
                    <option value="admin">Admin</option>
                    <option value="member">Team Member</option>
                </select>
            </div>
            
            <button type="submit" class="auth-btn">LOGIN</button>
        </form>
        
        <div id="authMessage"></div>
        
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; font-size: 12px;">
            <strong>Demo Credentials:</strong><br>
            Admin: admin / admin123<br>
            Member: member / member123
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="container hidden">
        <div class="header-bar">
            <div class="user-info">
                <span id="currentUser"></span> | <span id="currentRole"></span>
            </div>
            <button class="logout-btn" onclick="logout()">Logout</button>
        </div>

        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel hidden">
            <div class="admin-title">🔧 ADMIN CONTROL PANEL</div>
            <div class="button-group">
                <button class="export-btn" onclick="exportToJSON()">📄 Export JSON</button>
                <button class="export-btn" onclick="exportToExcel()">📊 Export Excel</button>
                <button class="export-btn" onclick="viewAllData()">👥 View All Data</button>
                <button class="export-btn" onclick="clearAllData()">🗑️ Clear Data</button>
            </div>
        </div>

        <div class="title">TOOLBOX MEETING</div>
        
        <!-- Tabel Indisipliner -->
        <div class="section">
            <table class="indisipliner-table">
                <tr>
                    <th class="no-col">No</th>
                    <th>Indisipliner</th>
                    <th class="no-col">No</th>
                    <th>Cost</th>
                </tr>
                <tr>
                    <td>I</td>
                    <td class="editable-cell"><input type="text" class="cell-input" placeholder="Isi indisipliner..."></td>
                    <td>1</td>
                    <td class="editable-cell"><input type="number" class="cell-input" placeholder="0"></td>
                </tr>
                <tr>
                    <td>S</td>
                    <td class="editable-cell"><input type="text" class="cell-input" placeholder="Isi indisipliner..."></td>
                    <td>2</td>
                    <td class="editable-cell"><input type="number" class="cell-input" placeholder="0"></td>
                </tr>
                <tr>
                    <td>A</td>
                    <td class="editable-cell"><input type="text" class="cell-input" placeholder="Isi indisipliner..."></td>
                    <td>3</td>
                    <td class="editable-cell"><input type="number" class="cell-input" placeholder="0"></td>
                </tr>
            </table>
        </div>

        <!-- Input PIC Alfa -->
        <div class="section">
            <input type="text" class="pic-input" placeholder="PIC Alfa" />
        </div>

        <!-- Target Hari Ini -->
        <div class="section">
            <div class="section-title">Target Hari Ini Tgl : <input type="date" style="margin-left: 10px; padding: 5px; border: 1px solid #ddd; border-radius: 3px;"></div>
            <div class="scrollable-table">
                <table id="targetTable">
                    <thead>
                        <tr>
                            <th class="no-col">No</th>
                            <th class="spk-col">SPK</th>
                            <th class="pic-col">PIC</th>
                            <th class="keterangan-col">Keterangan</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="data-row">
                            <td class="editable-cell"><input type="number" class="cell-input" placeholder="1"></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="SPK..."></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="PIC..."></td>
                            <td class="editable-cell keterangan-col"><input type="text" class="cell-input keterangan-input" placeholder="Keterangan..."></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <button id="addTargetBtn" class="add-row-btn" onclick="addRow('targetTable')">+ Tambah Baris</button>
        </div>

        <!-- Outstanding Hari Kemarin -->
        <div class="section">
            <div class="section-title">Outstanding Hari Kemarin Tgl : <input type="date" style="margin-left: 10px; padding: 5px; border: 1px solid #ddd; border-radius: 3px;"></div>
            <div class="scrollable-table">
                <table id="outstandingTable">
                    <thead>
                        <tr>
                            <th class="no-col">No</th>
                            <th class="spk-col">SPK</th>
                            <th class="pic-col">PIC</th>
                            <th class="keterangan-col">Keterangan</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="data-row">
                            <td class="editable-cell"><input type="number" class="cell-input" placeholder="1"></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="SPK..."></td>
                            <td class="editable-cell"><input type="text" class="cell-input" placeholder="PIC..."></td>
                            <td class="editable-cell keterangan-col"><input type="text" class="cell-input keterangan-input" placeholder="Keterangan..."></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <button id="addOutstandingBtn" class="add-row-btn" onclick="addRow('outstandingTable')">+ Tambah Baris</button>
        </div>

        <!-- Tombol Simpan -->
        <button id="saveBtn" class="save-btn" onclick="saveData()">💾 SIMPAN DATA</button>
    </div>

    <script>
        // User Management System
        const users = {
            'admin': { password: 'admin123', role: 'admin', name: 'Administrator' },
            'member': { password: 'member123', role: 'member', name: 'Team Member' }
        };

        let currentUser = null;
        let rowCounter = { targetTable: 2, outstandingTable: 2 };

        // Authentication Functions
        function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const role = document.getElementById('role').value;
            
            if (users[username] && users[username].password === password && users[username].role === role) {
                currentUser = {
                    username: username,
                    role: role,
                    name: users[username].name
                };
                
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                showMainApp();
            } else {
                showMessage('Username, password, atau role tidak valid!', 'error');
            }
        }

        function logout() {
            currentUser = null;
            localStorage.removeItem('currentUser');
            showAuthForm();
        }

        function showAuthForm() {
            document.getElementById('authContainer').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('hidden');
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            document.getElementById('role').value = '';
        }

        function showMainApp() {
            document.getElementById('authContainer').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            
            document.getElementById('currentUser').textContent = currentUser.name;
            document.getElementById('currentRole').textContent = currentUser.role.toUpperCase();
            
            if (currentUser.role === 'admin') {
                document.getElementById('adminPanel').classList.remove('hidden');
                enableEditMode();
            } else {
                document.getElementById('adminPanel').classList.add('hidden');
                enableReadOnlyMode();
            }
            
            loadData();
        }

        function enableEditMode() {
            const inputs = document.querySelectorAll('input, button');
            inputs.forEach(input => {
                input.disabled = false;
                input.classList.remove('readonly-input');
            });
            
            const cells = document.querySelectorAll('.editable-cell');
            cells.forEach(cell => {
                cell.classList.remove('readonly-cell');
            });
        }

        function enableReadOnlyMode() {
            const inputs = document.querySelectorAll('.cell-input, .pic-input, input[type="date"]');
            inputs.forEach(input => {
                input.disabled = true;
                input.classList.add('readonly-input');
            });
            
            const buttons = document.querySelectorAll('.add-row-btn, .save-btn');
            buttons.forEach(button => {
                button.style.display = 'none';
            });
            
            const cells = document.querySelectorAll('.editable-cell');
            cells.forEach(cell => {
                cell.classList.add('readonly-cell');
            });
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('authMessage');
            messageDiv.textContent = message;
            messageDiv.className = type === 'error' ? 'error-msg' : 'success-msg';
            
            setTimeout(() => {
                messageDiv.textContent = '';
                messageDiv.className = '';
            }, 3000);
        }

        // Data Management Functions
        function addRow(tableId) {
            if (currentUser.role !== 'admin') return;
            
            const table = document.getElementById(tableId);
            const tbody = table.querySelector('tbody');
            const newRow = document.createElement('tr');
            newRow.className = 'data-row';
            
            const rowNum = rowCounter[tableId];
            newRow.innerHTML = `
                <td class="editable-cell"><input type="number" class="cell-input" placeholder="${rowNum}"></td>
                <td class="editable-cell"><input type="text" class="cell-input" placeholder="SPK..."></td>
                <td class="editable-cell"><input type="text" class="cell-input" placeholder="PIC..."></td>
                <td class="editable-cell keterangan-col"><input type="text" class="cell-input keterangan-input" placeholder="Keterangan..."></td>
            `;
            
            tbody.appendChild(newRow);
            rowCounter[tableId]++;
        }

        function saveData() {
            if (currentUser.role !== 'admin') {
                alert('Hanya admin yang dapat menyimpan data!');
                return;
            }
            
            const data = {
                timestamp: new Date().toISOString(),
                savedBy: currentUser.username,
                indisipliner: [],
                picAlfa: document.querySelector('.pic-input').value,
                targetHariIni: {
                    tanggal: document.querySelector('input[type="date"]').value,
                    data: []
                },
                outstandingKemarin: {
                    tanggal: document.querySelectorAll('input[type="date"]')[1].value,
                    data: []
                }
            };

            // Collect data from all sections
            collectIndisiplinerData(data);
            collectTargetData(data);
            collectOutstandingData(data);

            // Save to localStorage with timestamp
            const allData = JSON.parse(localStorage.getItem('allToolboxData') || '[]');
            allData.push(data);
            localStorage.setItem('allToolboxData', JSON.stringify(allData));
            localStorage.setItem('currentToolboxData', JSON.stringify(data));
            
            alert('Data berhasil disimpan!');
        }

        function collectIndisiplinerData(data) {
            const indisiplinerRows = document.querySelectorAll('.indisipliner-table tr:not(:first-child)');
            indisiplinerRows.forEach((row, index) => {
                const inputs = row.querySelectorAll('input');
                data.indisipliner.push({
                    type: ['I', 'S', 'A'][index],
                    description: inputs[0].value,
                    cost: inputs[1].value
                });
            });
        }

        function collectTargetData(data) {
            const targetRows = document.querySelectorAll('#targetTable tbody tr');
            targetRows.forEach(row => {
                const inputs = row.querySelectorAll('input');
                if (inputs[1].value || inputs[2].value || inputs[3].value) {
                    data.targetHariIni.data.push({
                        no: inputs[0].value,
                        spk: inputs[1].value,
                        pic: inputs[2].value,
                        keterangan: inputs[3].value
                    });
                }
            });
        }

        function collectOutstandingData(data) {
            const outstandingRows = document.querySelectorAll('#outstandingTable tbody tr');
            outstandingRows.forEach(row => {
                const inputs = row.querySelectorAll('input');
                if (inputs[1].value || inputs[2].value || inputs[3].value) {
                    data.outstandingKemarin.data.push({
                        no: inputs[0].value,
                        spk: inputs[1].value,
                        pic: inputs[2].value,
                        keterangan: inputs[3].value
                    });
                }
            });
        }

        function loadData() {
            const savedData = localStorage.getItem('currentToolboxData');
            if (savedData) {
                const data = JSON.parse(savedData);
                
                // Load PIC Alfa
                if (data.picAlfa) {
                    document.querySelector('.pic-input').value = data.picAlfa;
                }
                
                // Load dates
                const dateInputs = document.querySelectorAll('input[type="date"]');
                if (data.targetHariIni.tanggal) {
                    dateInputs[0].value = data.targetHariIni.tanggal;
                }
                if (data.outstandingKemarin.tanggal) {
                    dateInputs[1].value = data.outstandingKemarin.tanggal;
                }
                
                // Load indisipliner data
                if (data.indisipliner) {
                    const indisiplinerInputs = document.querySelectorAll('.indisipliner-table input');
                    data.indisipliner.forEach((item, index) => {
                        if (indisiplinerInputs[index * 2]) {
                            indisiplinerInputs[index * 2].value = item.description || '';
                        }
                        if (indisiplinerInputs[index * 2 + 1]) {
                            indisiplinerInputs[index * 2 + 1].value = item.cost || '';
                        }
                    });
                }
            }
        }

        // Admin Functions
        function exportToJSON() {
            const allData = JSON.parse(localStorage.getItem('allToolboxData') || '[]');
            const dataStr = JSON.stringify(allData, null, 2);
            downloadFile(dataStr, `toolbox_meeting_all_${new Date().toISOString().split('T')[0]}.json`, 'application/json');
        }

        function exportToExcel() {
            const allData = JSON.parse(localStorage.getItem('allToolboxData') || '[]');
            let csvContent = "Timestamp,Saved By,PIC Alfa,Target Date,Outstanding Date,Indisipliner I,Cost I,Indisipliner S,Cost S,Indisipliner A,Cost A\n";
            
            allData.forEach(data => {
                const row = [
                    data.timestamp,
                    data.savedBy,
                    data.picAlfa,
                    data.targetHariIni.tanggal,
                    data.outstandingKemarin.tanggal,
                    data.indisipliner[0]?.description || '',
                    data.indisipliner[0]?.cost || '',
                    data.indisipliner[1]?.description || '',
                    data.indisipliner[1]?.cost || '',
                    data.indisipliner[2]?.description || '',
                    data.indisipliner[2]?.cost || ''
                ].map(field => `"${field}"`).join(',');
                csvContent += row + '\n';
            });
            
            downloadFile(csvContent, `toolbox_meeting_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv');
        }

        function viewAllData() {
            const allData = JSON.parse(localStorage.getItem('allToolboxData') || '[]');
            if (allData.length === 0) {
                alert('Tidak ada data tersimpan.');
                return;
            }
            
            let summary = `📊 SUMMARY DATA TOOLBOX MEETING\n\n`;
            summary += `Total Records: ${allData.length}\n\n`;
            
            allData.forEach((data, index) => {
                summary += `${index + 1}. ${new Date(data.timestamp).toLocaleString()}\n`;
                summary += `   Saved by: ${data.savedBy}\n`;
                summary += `   PIC Alfa: ${data.picAlfa}\n`;
                summary += `   Target: ${data.targetHariIni.tanggal}\n`;
                summary += `   Outstanding: ${data.outstandingKemarin.tanggal}\n\n`;
            });
            
            alert(summary);
        }

        function clearAllData() {
            if (confirm('⚠️ PERINGATAN!\n\nApakah Anda yakin ingin menghapus SEMUA data?\nTindakan ini tidak dapat dibatalkan!')) {
                localStorage.removeItem('allToolboxData');
                localStorage.removeItem('currentToolboxData');
                alert('✅ Semua data berhasil dihapus!');
                location.reload();
            }
        }

        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        // Initialize App
        window.onload = function() {
            const savedUser = localStorage.getItem('currentUser');
            if (savedUser) {
                currentUser = JSON.parse(savedUser);
                showMainApp();
            } else {
                showAuthForm();
            }
        };
    </script>
</body>
</html>
