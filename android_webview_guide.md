# Panduan Menjalankan Toolbox Meeting di Android

## Metode 1: Menggunakan Android WebView (Recommended)

### Langkah-langkah:

1. **Buat Project Android Studio Baru**
   - Pilih "Empty Activity"
   - Nama: ToolboxMeeting
   - Package: com.yourcompany.toolboxmeeting

2. **Edit MainActivity.java**
```java
package com.yourcompany.toolboxmeeting;

import android.os.Bundle;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {
    private WebView webView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        webView = findViewById(R.id.webview);
        
        // Enable JavaScript
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowFileAccess(true);
        
        // Set WebView client
        webView.setWebViewClient(new WebViewClient());
        
        // Load HTML file from assets
        webView.loadUrl("file:///android_asset/toolbox_meeting.html");
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
}
```

3. **Edit activity_main.xml**
```xml
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
```

4. **Edit AndroidManifest.xml**
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

5. **Copy file HTML**
   - Buat folder `assets` di `app/src/main/`
   - Copy file `toolbox_meeting.html` ke folder `assets`

## Metode 2: Menggunakan Browser Android

### Langkah-langkah:
1. Copy file `toolbox_meeting.html` ke storage Android
2. Buka dengan browser (Chrome, Firefox, dll)
3. Bookmark untuk akses mudah

## Metode 3: Menggunakan Aplikasi WebView Siap Pakai

### Aplikasi yang bisa digunakan:
- **WebView App** (Play Store)
- **Hermit** (Lite Apps Browser)
- **Native Alpha** (WebView wrapper)

### Cara menggunakan:
1. Install salah satu aplikasi di atas
2. Upload file HTML ke cloud storage (Google Drive, Dropbox)
3. Buat link public dan buka di aplikasi WebView

## Metode 4: Hosting Online (Paling Praktis)

### Platform hosting gratis:
- **GitHub Pages**
- **Netlify**
- **Vercel**
- **Firebase Hosting**

### Langkah GitHub Pages:
1. Buat repository GitHub baru
2. Upload file `toolbox_meeting.html`
3. Enable GitHub Pages di Settings
4. Akses via URL: `https://username.github.io/repository-name/toolbox_meeting.html`

## Rekomendasi:

**Untuk penggunaan internal/pribadi:** Metode 1 (Android WebView)
**Untuk sharing dengan tim:** Metode 4 (Hosting online)
**Untuk testing cepat:** Metode 2 (Browser Android)

## Tips Optimasi Android:
- File HTML sudah dioptimasi untuk mobile
- Gunakan viewport meta tag (sudah ada)
- Touch-friendly button sizes (sudah ada)
- Responsive design (sudah ada)
