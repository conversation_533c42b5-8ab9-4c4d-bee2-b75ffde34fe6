# 📖 User Guide - Toolbox Meeting System

## 🎯 Sistem Overview
Aplikasi web untuk manajemen Toolbox Meeting dengan dua level akses:
- **Admin**: Full access untuk input, edit, dan export data
- **Team Member**: Read-only access untuk melihat data

## 🔐 Login System

### Akses Aplikasi:
1. Buka URL: `[YOUR-DEPLOYMENT-URL]/toolbox_meeting_with_auth.html`
2. <PERSON><PERSON><PERSON><PERSON> credentials sesuai role
3. <PERSON>lih role yang sesuai dari dropdown

### Default Login Credentials:
```
👨‍💼 ADMIN ACCESS:
Username: admin
Password: admin123
Role: Admin

👥 TEAM MEMBER ACCESS:
Username: member
Password: member123
Role: Team Member
```

---

## 👨‍💼 PANDUAN ADMIN

### 🚀 Login sebagai Admin
1. Masukkan username: `admin`
2. Masukkan password: `admin123`
3. Pilih role: `Admin`
4. Klik `LOGIN`

### 🎛️ Admin Control Panel
Setelah login, Anda akan melihat **Admin Control Panel** dengan tombol:

#### 📄 Export JSON
- **Fungsi**: Download semua data dalam format JSON
- **Ka<PERSON> digunakan**: Backup data, transfer ke sistem lain
- **File output**: `toolbox_meeting_all_YYYY-MM-DD.json`

#### 📊 Export Excel
- **Fungsi**: Download data dalam format CSV (bisa dibuka di Excel)
- **Kapan digunakan**: Analisis data, reporting
- **File output**: `toolbox_meeting_YYYY-MM-DD.csv`

#### 👥 View All Data
- **Fungsi**: Lihat summary semua data yang tersimpan
- **Info yang ditampilkan**: 
  - Total records
  - Timestamp setiap entry
  - User yang menyimpan
  - Tanggal target dan outstanding

#### 🗑️ Clear Data
- **Fungsi**: Hapus SEMUA data (HATI-HATI!)
- **Konfirmasi**: Double confirmation required
- **Backup**: Pastikan export data dulu sebelum clear

### ✏️ Input Data Toolbox Meeting

#### 1. Tabel Indisipliner
- **Kolom I, S, A**: Isi deskripsi indisipliner
- **Kolom Cost**: Masukkan nilai biaya (angka)
- **Tips**: Klik langsung pada cell untuk edit

#### 2. PIC Alfa
- **Field**: Input nama PIC yang bertanggung jawab
- **Format**: Nama lengkap atau inisial

#### 3. Target Hari Ini
- **Tanggal**: Pilih tanggal menggunakan date picker
- **Tabel**: Isi No, SPK, PIC, dan Keterangan
- **Tambah Baris**: Klik `+ Tambah Baris` untuk row baru
- **Auto-numbering**: Nomor otomatis increment

#### 4. Outstanding Hari Kemarin
- **Tanggal**: Pilih tanggal kemarin
- **Tabel**: Sama seperti Target Hari Ini
- **Scroll**: Tabel bisa di-scroll independen

### 💾 Menyimpan Data
1. Pastikan semua field terisi
2. Klik tombol `💾 SIMPAN DATA`
3. Konfirmasi "Data berhasil disimpan!"
4. File JSON otomatis ter-download sebagai backup

### 🔄 Data Persistence
- **Auto-save**: Data tersimpan di browser localStorage
- **Auto-load**: Data otomatis dimuat saat login
- **History**: Semua versi data tersimpan
- **Backup**: Auto-download JSON setiap save

---

## 👥 PANDUAN TEAM MEMBER

### 🚀 Login sebagai Team Member
1. Masukkan username: `member`
2. Masukkan password: `member123`
3. Pilih role: `Team Member`
4. Klik `LOGIN`

### 👀 View-Only Mode
Setelah login sebagai member:
- ✅ **Bisa melihat**: Semua data yang diinput admin
- ❌ **Tidak bisa**: Edit, tambah, atau hapus data
- ❌ **Tidak ada**: Tombol save, tambah baris, atau admin panel

### 📱 Interface Team Member
- **Header**: Menampilkan nama dan role "TEAM MEMBER"
- **Data Display**: Semua tabel dalam mode read-only
- **Styling**: Field input berwarna abu-abu (disabled)
- **Scroll**: Tetap bisa scroll untuk melihat data lengkap

### 🔄 Real-time Updates
- **Auto-refresh**: Data otomatis update saat admin menyimpan
- **Sync**: Refresh browser untuk melihat data terbaru
- **Notification**: Tidak ada notifikasi real-time (manual refresh)

---

## 📱 MOBILE USAGE

### Android/iOS Browser
1. **Buka browser** (Chrome, Safari, Firefox)
2. **Akses URL** aplikasi
3. **Login** sesuai role
4. **Add to Home Screen** untuk akses cepat:
   - Android Chrome: Menu → Add to Home screen
   - iOS Safari: Share → Add to Home Screen

### Touch Optimization
- **Tap-friendly**: Semua button dan input optimized untuk touch
- **Zoom**: Pinch to zoom supported
- **Scroll**: Smooth scrolling pada tabel
- **Keyboard**: Auto-popup keyboard untuk input

---

## 🛠️ TROUBLESHOOTING

### Login Issues
**Problem**: Login gagal
**Solution**: 
- Check username/password case-sensitive
- Pastikan role sesuai dengan user
- Clear browser cache
- Try incognito/private mode

### Data Tidak Muncul
**Problem**: Data kosong setelah login
**Solution**:
- Refresh browser (F5 atau pull-to-refresh)
- Check localStorage enabled di browser
- Login sebagai admin untuk input data baru

### Mobile Display Issues
**Problem**: Layout tidak responsive
**Solution**:
- Rotate device (portrait/landscape)
- Zoom out untuk full view
- Use latest browser version
- Clear browser cache

### Export Tidak Bekerja
**Problem**: File tidak ter-download
**Solution**:
- Check browser download permissions
- Disable popup blocker
- Try different browser
- Check storage space

---

## 💡 TIPS & BEST PRACTICES

### Untuk Admin:
- 📅 **Daily backup**: Export data setiap hari
- 🔄 **Regular save**: Save data setelah setiap input session
- 👥 **Team communication**: Inform team saat ada update data
- 🔐 **Password security**: Ganti password default secara berkala

### Untuk Team Member:
- 🔄 **Refresh regularly**: Refresh browser untuk data terbaru
- 📱 **Bookmark**: Add to home screen untuk akses cepat
- 📞 **Contact admin**: Hubungi admin jika ada data yang perlu diupdate
- 💬 **Feedback**: Berikan feedback untuk improvement

### General:
- 🌐 **Stable connection**: Pastikan koneksi internet stabil
- 🔋 **Battery**: Charge device untuk session panjang
- 💾 **Backup**: Selalu backup data penting
- 📖 **Training**: Pastikan semua user familiar dengan sistem

---

## 📞 SUPPORT

### Technical Issues:
- **Admin contact**: <EMAIL>
- **IT Support**: <EMAIL>
- **Emergency**: Call IT helpdesk

### Feature Requests:
- Submit via email dengan detail requirement
- Include screenshots jika perlu
- Specify priority level

### Training:
- **New user training**: 10 menit per user
- **Admin training**: 30 menit
- **Refresher training**: Available on request

---

## 📊 DATA SECURITY

### Data Storage:
- **Local**: Data tersimpan di browser localStorage
- **Backup**: Manual export ke file JSON/CSV
- **Sharing**: Via file export/import
- **Retention**: Data tersimpan sampai manual delete

### Access Control:
- **Role-based**: Admin vs Member access
- **Session**: Auto-logout saat close browser
- **Password**: Change default passwords
- **Audit**: Track who saved what data

### Privacy:
- **No cloud**: Data tidak tersimpan di server external
- **Local only**: Semua data di device masing-masing
- **Manual sync**: Sharing data via export/import
- **Control**: Full control atas data sendiri
