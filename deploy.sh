#!/bin/bash

# 🚀 Toolbox Meeting System - Auto Deployment Script
# Supports: GitHub Pages, Netlify, Vercel

echo "🚀 TOOLBOX MEETING DEPLOYMENT SCRIPT"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if git is installed
check_git() {
    if ! command -v git &> /dev/null; then
        print_error "Git is not installed. Please install Git first."
        exit 1
    fi
    print_status "Git is installed ✓"
}

# Check if files exist
check_files() {
    required_files=("toolbox_meeting_with_auth.html" "deployment_guide.md" "user_guide.md")
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            print_error "Required file missing: $file"
            exit 1
        fi
    done
    print_status "All required files found ✓"
}

# Create additional files for deployment
create_deployment_files() {
    print_status "Creating deployment files..."
    
    # Create index.html that redirects to main app
    cat > index.html << 'EOF'
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toolbox Meeting System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #FFD700, #FFA500);
        }
        .container {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            max-width: 400px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            background: #FFD700;
            color: #333;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 10px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #e6c200;
        }
        .info {
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🛠️ TOOLBOX MEETING SYSTEM</div>
        <p>Sistem manajemen Toolbox Meeting untuk tim Anda</p>
        
        <a href="toolbox_meeting_with_auth.html" class="btn">🚀 MASUK APLIKASI</a>
        
        <div class="info">
            <p><strong>Demo Credentials:</strong></p>
            <p>Admin: admin / admin123</p>
            <p>Member: member / member123</p>
        </div>
    </div>
</body>
</html>
EOF

    # Create README.md
    cat > README.md << 'EOF'
# 🛠️ Toolbox Meeting System

Sistem manajemen Toolbox Meeting dengan fitur autentikasi dan role-based access control.

## 🚀 Quick Start

1. **Akses Aplikasi**: [Live Demo](https://your-username.github.io/toolbox-meeting/)
2. **Login Admin**: username: `admin`, password: `admin123`
3. **Login Member**: username: `member`, password: `member123`

## 📋 Features

- ✅ Role-based access (Admin/Member)
- ✅ Data persistence dengan localStorage
- ✅ Export data (JSON/Excel)
- ✅ Mobile responsive design
- ✅ Real-time data sync

## 📖 Documentation

- [Deployment Guide](deployment_guide.md)
- [User Guide](user_guide.md)

## 🔧 Tech Stack

- HTML5/CSS3/JavaScript
- LocalStorage for data persistence
- Responsive design for mobile

## 📞 Support

For technical support, please contact your system administrator.
EOF

    # Create .gitignore
    cat > .gitignore << 'EOF'
# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor files
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log

# Temporary files
*.tmp
*.temp
EOF

    print_status "Deployment files created ✓"
}

# Initialize git repository
init_git() {
    if [[ ! -d ".git" ]]; then
        print_status "Initializing Git repository..."
        git init
        git add .
        git commit -m "Initial commit: Toolbox Meeting System"
        print_status "Git repository initialized ✓"
    else
        print_status "Git repository already exists ✓"
    fi
}

# Deploy to GitHub Pages
deploy_github() {
    print_header "🐙 GITHUB PAGES DEPLOYMENT"
    
    read -p "Enter your GitHub username: " github_username
    read -p "Enter repository name (default: toolbox-meeting): " repo_name
    repo_name=${repo_name:-toolbox-meeting}
    
    print_status "Setting up GitHub remote..."
    git remote add origin "https://github.com/$github_username/$repo_name.git" 2>/dev/null || true
    
    print_status "Pushing to GitHub..."
    git branch -M main
    git push -u origin main
    
    print_status "🎉 Deployment completed!"
    print_status "Your app will be available at:"
    echo -e "${GREEN}https://$github_username.github.io/$repo_name/${NC}"
    echo ""
    print_warning "Note: GitHub Pages may take 5-10 minutes to become available"
    print_warning "Enable GitHub Pages in repository Settings > Pages"
}

# Deploy to Netlify
deploy_netlify() {
    print_header "🌐 NETLIFY DEPLOYMENT"
    
    print_status "Creating Netlify deployment package..."
    
    # Create netlify.toml
    cat > netlify.toml << 'EOF'
[build]
  publish = "."

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NODE_VERSION = "16"
EOF

    print_status "Netlify configuration created ✓"
    print_status "Manual steps required:"
    echo "1. Go to https://netlify.com"
    echo "2. Drag and drop this folder to Netlify"
    echo "3. Your app will be deployed instantly"
    echo "4. Custom domain available in site settings"
}

# Deploy to Vercel
deploy_vercel() {
    print_header "▲ VERCEL DEPLOYMENT"
    
    # Create vercel.json
    cat > vercel.json << 'EOF'
{
  "version": 2,
  "builds": [
    {
      "src": "**/*",
      "use": "@vercel/static"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/$1"
    }
  ]
}
EOF

    print_status "Vercel configuration created ✓"
    print_status "Manual steps required:"
    echo "1. Go to https://vercel.com"
    echo "2. Import this Git repository"
    echo "3. Deploy automatically"
    echo "4. Custom domain available in project settings"
}

# Main deployment menu
main_menu() {
    print_header "📦 SELECT DEPLOYMENT PLATFORM"
    echo "1. GitHub Pages (Recommended)"
    echo "2. Netlify"
    echo "3. Vercel"
    echo "4. Create files only (manual deployment)"
    echo "5. Exit"
    echo ""
    read -p "Choose option (1-5): " choice
    
    case $choice in
        1)
            deploy_github
            ;;
        2)
            deploy_netlify
            ;;
        3)
            deploy_vercel
            ;;
        4)
            print_status "Files created. You can now deploy manually."
            ;;
        5)
            print_status "Deployment cancelled."
            exit 0
            ;;
        *)
            print_error "Invalid option. Please choose 1-5."
            main_menu
            ;;
    esac
}

# Main execution
main() {
    print_header "🛠️ TOOLBOX MEETING SYSTEM DEPLOYMENT"
    echo ""
    
    # Run checks
    check_git
    check_files
    
    # Create deployment files
    create_deployment_files
    
    # Initialize git
    init_git
    
    # Show deployment options
    main_menu
    
    print_status "🎉 Deployment process completed!"
    echo ""
    print_header "📋 NEXT STEPS:"
    echo "1. Test your deployed application"
    echo "2. Share URL with your team"
    echo "3. Update default passwords"
    echo "4. Setup regular data backups"
    echo ""
    print_status "For detailed instructions, see deployment_guide.md"
}

# Run main function
main
